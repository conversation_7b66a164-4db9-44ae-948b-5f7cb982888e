@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tu CSS existente a continuación */

body {
  margin: 0;
  @apply bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 min-h-screen text-gray-100;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Variables CSS personalizadas */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Estilos base mejorados */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

/* Animaciones personalizadas */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Clases de utilidad para animaciones */
.animate-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

.animate-zoom-in {
  animation: zoomIn 0.3s ease-out;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Efectos de hover mejorados */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Gradientes de fondo */
.bg-gradient-primary {
  background: var(--primary-gradient);
}

.bg-gradient-secondary {
  background: var(--secondary-gradient);
}

.bg-gradient-success {
  background: var(--success-gradient);
}

.bg-gradient-warning {
  background: var(--warning-gradient);
}

.bg-gradient-danger {
  background: var(--danger-gradient);
}

/* Efectos de cristal */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Sombras personalizadas */
.shadow-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.shadow-glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
}

.shadow-glow-red {
  box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

/* Transiciones suaves */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Utilidades para texto */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ─── Ocultar flechitas de los inputs number ─── */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield;
}

/* ─── Animaciones y utilidades ─── */
.bg-gray-850 {
  background-color: #1f2937;
}

/* Animaciones personalizadas */
@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6); }
}

.animate-gradient {
  animation: gradient 3s ease infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Mejoras de accesibilidad - Versión menos restrictiva */
@media (prefers-reduced-motion: reduce) {
  /* Solo desactivar animaciones complejas, mantener transiciones básicas */
  .animate-gradient,
  .animate-glow,
  .animate-bounce,
  .animate-pulse,
  .animate-spin {
    animation: none !important;
  }

  /* Reducir duración de transiciones pero no eliminarlas completamente */
  .transition-all,
  .transition-transform,
  .transition-colors {
    transition-duration: 0.1s !important;
  }
}

/* Mejoras para dispositivos táctiles */
@media (hover: none) and (pointer: coarse) {
  .hover\:scale-105:hover {
    transform: none;
  }

  .hover\:bg-gray-600:hover {
    background-color: inherit;
  }
}

/* Estilos para tooltips mejorados */
.react-tooltip {
  z-index: 9999 !important;
  max-width: 300px !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

/* Mejoras para el grid del inventario */
.inventory-grid {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

/* Efectos de hover mejorados */
.inventory-item:hover {
  z-index: 100;
  transform: scale(1.02);
  transition: all 0.2s ease;
}

/* Indicadores de estado */
.status-indicator {
  position: relative;
}

.status-indicator::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  border: 2px solid #1f2937;
}

.status-indicator.error::after {
  background: #ef4444;
}

.status-indicator.warning::after {
  background: #f59e0b;
}

/* ─── Aurora minimalista para actores simultáneos ─── */
@keyframes borderPulse {
  0%, 100% { 
    border-color: #f59e0b;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.3);
  }
  50% { 
    border-color: #fbbf24;
    box-shadow: 0 0 12px rgba(251, 191, 36, 0.4);
  }
}

.simul {
  background: linear-gradient(135deg, #1e293b, #334155, #475569);
  border: 2px solid;
  animation: borderPulse 4s ease-in-out infinite;
  position: relative;
}

/* Ocultar barra de scroll */
.scrollbar-hide {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Scrollbar util classes */
.scrollbar-thin {
  scrollbar-width: thin;
}
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}
.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}
.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}
