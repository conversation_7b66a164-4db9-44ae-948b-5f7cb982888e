{"name": "fichas-rol-app", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "firebase": "^11.8.1", "framer-motion": "^12.19.1", "konva": "^9.3.20", "nanoid": "^3.3.8", "react": "^19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^19.1.0", "react-flip-move": "^3.0.5", "react-icons": "^5.5.0", "react-konva": "^19.0.7", "react-scripts": "5.0.1", "react-tooltip": "^5.28.1", "use-image": "^1.1.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "format": "prettier --write \"src/**/*.{js,jsx}\"", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prettier": "^3.2.5", "tailwindcss": "^3.4.17"}}